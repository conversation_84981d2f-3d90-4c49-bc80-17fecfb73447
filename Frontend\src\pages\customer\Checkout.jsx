import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { useCart } from "../../context/CartContext";
import { useAuth } from "../../context/AuthContext";
import { useOrder } from "../../context/OrderContext";
import { useLoyalty } from "../../context/LoyaltyContext";
import { useScheduling } from "../../context/SchedulingContext";
import {
  ArrowLeft,
  MapPin,
  CreditCard,
  AlertCircle,
  Wallet,
  DollarSign,
  Calendar,
  Clock,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";
import AddressSelector from "../../components/address/AddressSelector";
import useAddresses from "../../hooks/useAddresses";
import { useDeliveryFee } from "../../hooks/useDeliveryFee";
import DeliveryFeeDisplay from "../../components/delivery/DeliveryFeeDisplay";
import ScheduleOrderModal from "../../components/scheduling/ScheduleOrderModal";

const Checkout = () => {
  const { cart, clearCart } = useCart();
  const { user } = useAuth();
  const { createOrder, loading: orderLoading, error: orderError } = useOrder();
  const { processOrderCompletion, loyaltyData, LOYALTY_TIERS } = useLoyalty();
  const { scheduleOrder, isScheduled, setIsScheduled, selectedDateTime } =
    useScheduling();
  const navigate = useNavigate();

  // Payment method state
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState("cash_on_delivery");

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: user?.name || "",
      phone: user?.phone || "",
      address: user?.address || "",
      notes: "",
    },
  });

  const [loading, setLoading] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [error, setError] = useState(null);
  const [orderPlaced, setOrderPlaced] = useState(false);

  // Clear error when component mounts or cart changes
  useEffect(() => {
    setError(null);
  }, [cart]);

  // Address management - using new robust system
  const {
    selectedAddress,
    selectAddress,
    hasSelectedAddress,
    getOrderAddressId,
    validateAddressForOrder,
  } = useAddresses();

  // Helper: Get valid address ID for order (using new robust system)
  const getValidOrderAddressId = async () => {
    try {
      console.log("🔍 Getting valid order address ID...");

      // Use the new address service to get a valid address ID
      const addressId = await getOrderAddressId();

      console.log("✅ Valid order address ID:", addressId);
      return addressId;
    } catch (error) {
      console.error("❌ Failed to get valid order address ID:", error);
      return null;
    }
  };

  // Dynamic delivery fee calculation
  const {
    deliveryInfo,
    deliveryFee,
    loading: deliveryLoading,
  } = useDeliveryFee(cart.restaurantId, {
    orderAmount: cart.total,
    autoCalculate: hasSelectedAddress,
  });
  const processingSteps = [
    "Validating order information...",
    "Processing your order...",
    "Sending order to restaurant...",
    "Finalizing your order...",
  ];

  // Debug cart state
  useEffect(() => {
    console.log("🔍 Checkout page - Cart state:", cart);
    console.log("🔍 Cart items length:", cart.items?.length || 0);
    console.log("🔍 Cart restaurant ID:", cart.restaurantId);
  }, [cart]);

  // Redirect to cart if empty - use useEffect to avoid setState during render
  // But don't redirect if we're currently processing an order
  useEffect(() => {
    if (!cart.items || cart.items.length === 0) {
      if (!loading) {
        console.log(
          "🔍 Cart appears empty, checking if this is a timing issue..."
        );
        console.log("🔍 Cart object:", cart);
        console.log("🔍 Cart items:", cart.items);

        // Add a small delay to handle potential timing issues
        const redirectTimer = setTimeout(() => {
          if (!cart.items || cart.items.length === 0) {
            console.log(
              "🔍 Confirmed empty cart after delay - redirecting to cart"
            );
            navigate("/cart");
          } else {
            console.log("🔍 Cart has items after delay - staying on checkout");
          }
        }, 100); // 100ms delay

        return () => clearTimeout(redirectTimer);
      }
    }
  }, [cart.items, navigate, loading, cart]);

  // Don't render if cart is empty (unless we're processing an order)
  // Add a small grace period for cart loading
  if (!cart.items || (cart.items.length === 0 && !loading)) {
    return (
      <div className='flex justify-center items-center bg-gray-50 min-h-screen'>
        <div className='text-center'>
          <p className='text-gray-600'>Loading cart...</p>
        </div>
      </div>
    );
  }

  const onSubmit = async (data) => {
    setError(null);

    console.log("🔍 Form submitted with data:", data);
    console.log("🔍 Form errors:", errors);
    console.log("🔍 Cart at form submission:", cart);

    // FIRST: Basic cart validation
    if (!cart || typeof cart !== "object") {
      console.error("❌ Cart is not properly initialized:", cart);
      setError(
        "Cart data is not available. Please refresh the page and try again."
      );
      return;
    }

    if (!cart.items || !Array.isArray(cart.items) || cart.items.length === 0) {
      console.error("❌ Cart has no items:", cart.items);
      setError("Your cart is empty. Please add items before checkout.");
      navigate("/cart");
      return;
    }

    // Check if there are any form validation errors
    if (Object.keys(errors).length > 0) {
      console.log("❌ Form has validation errors:", errors);
      setError("Please fix the form errors before submitting.");
      return;
    }

    // SECOND: Validate restaurant ID before any processing
    console.log("🔍 Starting checkout validation...");
    console.log("🔍 Full cart object:", cart);

    // Get restaurant ID from cart - try multiple possible field names
    let validatedRestaurantId =
      cart.restaurantId || cart.restaurant_id || cart.restaurant?.id;

    // If still no restaurant ID, try to get it from the first cart item
    if (!validatedRestaurantId && cart.items && cart.items.length > 0) {
      const firstItem = cart.items[0];
      validatedRestaurantId = firstItem.restaurant_id || firstItem.restaurantId;
      console.log(
        "🔍 Trying to get restaurant ID from first item:",
        validatedRestaurantId
      );

      // If we found restaurant ID in item, also try to get restaurant name
      if (validatedRestaurantId && !cart.restaurantName) {
        const restaurantName =
          firstItem.restaurant_name || firstItem.restaurantName;
        console.log("🔍 Found restaurant name in item:", restaurantName);
      }
    }

    // If still no restaurant ID, check if we can extract it from the current URL
    if (!validatedRestaurantId) {
      const currentPath = window.location.pathname;
      const restaurantMatch = currentPath.match(/\/restaurants\/(\d+)/);
      if (restaurantMatch) {
        validatedRestaurantId = parseInt(restaurantMatch[1], 10);
        console.log(
          "🔍 Extracted restaurant ID from URL:",
          validatedRestaurantId
        );
      }
    }

    // Convert to integer to ensure proper type
    if (validatedRestaurantId) {
      validatedRestaurantId = parseInt(validatedRestaurantId, 10);
    }

    console.log("🔍 Final restaurant ID:", validatedRestaurantId);
    console.log("🔍 Cart items count:", cart.items?.length || 0);

    // Validate restaurant ID FIRST
    if (!validatedRestaurantId || isNaN(validatedRestaurantId)) {
      console.error(
        "❌ No restaurant ID found in cart or invalid restaurant ID!"
      );
      console.error("❌ Cart structure:", {
        restaurantId: cart.restaurantId,
        restaurant_id: cart.restaurant_id,
        restaurant: cart.restaurant,
        itemsCount: cart.items?.length || 0,
        firstItem: cart.items?.[0],
      });

      setError(
        "Unable to determine restaurant information from your cart. This may be due to corrupted cart data."
      );
      return;
    }

    // SECOND: Validate cart has items
    if (!cart.items || cart.items.length === 0) {
      setError("Your cart is empty. Please add items before checkout.");
      return;
    }

    setLoading(true);

    try {
      // Show processing steps with delays
      const stepInterval = setInterval(() => {
        setProcessingStep((prev) => {
          if (prev < processingSteps.length - 1) {
            return prev + 1;
          } else {
            clearInterval(stepInterval);
            return prev;
          }
        });
      }, 700);

      // Get delivery address ID using new robust system
      const deliveryAddressId = await getValidOrderAddressId();

      console.log("🔍 Checkout Debug - Address validation:");
      console.log("  Selected address:", selectedAddress);
      console.log("  Delivery address ID:", deliveryAddressId);

      // Validate address selection
      if (!deliveryAddressId) {
        setError(
          "Please select a delivery address before placing your order. You need to have a saved address to place an order."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Validate the selected address for order placement
      if (selectedAddress) {
        const validation = validateAddressForOrder(selectedAddress);
        if (!validation.valid) {
          setError(
            `Address validation failed: ${validation.error}. Please select a valid saved address.`
          );
          setLoading(false);
          clearInterval(stepInterval);
          return;
        }
      }

      console.log("✅ Using delivery address ID:", deliveryAddressId);

      // Validate that the address is valid for delivery
      if (!selectedAddress) {
        setError("Please select a delivery address before placing your order.");
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Check if address has a backend ID (meaning it's saved in the database)
      // or if it's a current location that can be used for delivery
      const hasBackendId =
        selectedAddress.backendId ||
        (selectedAddress.id &&
          !selectedAddress.id.toString().startsWith("current_"));
      const isValidForDelivery =
        selectedAddress.type === "saved" ||
        (selectedAddress.type === "current" && selectedAddress.coordinates) ||
        hasBackendId;

      if (!isValidForDelivery) {
        setError(
          "Please select a saved delivery address or add your current location as a saved address. Temporary addresses cannot be used for orders."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // For current location addresses, we need to save them first
      if (selectedAddress.type === "current" && !selectedAddress.backendId) {
        setError(
          "Please save your current location as a delivery address before placing your order. Click 'Add Address' to save it."
        );
        setLoading(false);
        clearInterval(stepInterval);
        return;
      }

      // Restaurant ID already validated at the beginning of the function

      // Prepare order data for API
      const orderData = {
        delivery_address: deliveryAddressId,
        restaurant: validatedRestaurantId,
        payment_method: selectedPaymentMethod || "cash_on_delivery",
        special_instructions: data.notes || "",
        items: cart.items.map((item) => ({
          menu_item_id: item.id,
          quantity: item.quantity,
          special_requests: item.special_requests || "",
        })),
      };

      // Debug logging
      console.log("🔍 Order Data being sent:", orderData);
      console.log("🔍 Selected address ID:", deliveryAddressId);

      // Create order via API
      console.log("📡 Sending order creation request...");
      const result = await createOrder(orderData);
      console.log("📡 Order creation response:", result);

      if (result.success && result.data && result.data.id) {
        console.log("🎉 Order created successfully:", result.data);
        console.log("🔍 Order ID:", result.data.id);

        // Award loyalty points for the order
        if (processOrderCompletion) {
          const pointsResult = processOrderCompletion(
            cart.total,
            result.data.id
          );
          if (pointsResult?.success) {
            console.log(`Awarded loyalty points for order ${result.data.id}`);
          }
        }

        // Stop loading and clear interval first
        clearInterval(stepInterval);
        setLoading(false);

        // Store order ID for potential recovery
        const orderId = result.data.id;
        console.log(
          "🚀 Navigating to order confirmation:",
          `/order-confirmation/${orderId}`
        );

        // Navigate first, then clear cart after a short delay
        navigate(`/order-confirmation/${orderId}`);

        // Clear cart after navigation is initiated, but only once
        if (!orderPlaced) {
          setTimeout(() => {
            console.log(
              "🧹 Clearing cart after successful order placement and navigation"
            );
            clearCart();
            setOrderPlaced(true);
          }, 200);
        } else {
          console.log("🛡️ Order already placed, cart clearing skipped");
        }
      } else {
        // Handle error - order creation failed
        console.error("❌ Order creation failed:", result);
        console.error("❌ Result success:", result.success);
        console.error("❌ Result data:", result.data);
        console.error("❌ Result error:", result.error);

        clearInterval(stepInterval);
        setLoading(false);
        setError(result.error || "Failed to create order. Please try again.");
      }
    } catch (error) {
      console.error("❌ Order submission error:", error);
      console.error("🔍 Full error object:", error);
      console.error("🔍 Error response:", error.response?.data);
      console.error("🔍 Error status:", error.response?.status);
      console.error("🔍 Error message:", error.message);

      clearInterval(stepInterval);
      setLoading(false);

      // DO NOT clear cart on error - user should be able to retry
      console.log("🛡️ Preserving cart contents due to order creation error");

      // Show specific error message if available
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "An error occurred while placing your order. Please try again.";

      setError(errorMessage);
    }
  };

  const handleScheduleOrder = (schedulingData) => {
    setIsScheduled(true);
    setShowScheduleModal(false);
  };

  const handleOrderNow = () => {
    setIsScheduled(false);
  };

  return (
    <div className='relative mx-auto px-4 py-8 max-w-4xl animate-fade-in container'>
      {/* Processing Overlay */}
      {loading && (
        <div className='z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50'>
          <div className='bg-white shadow-lg p-8 rounded-lg w-full max-w-md text-center'>
            <div className='mx-auto mb-4 border-primary-500 border-t-4 border-b-4 rounded-full w-16 h-16 animate-spin'></div>
            <h3 className='mb-2 font-semibold text-xl'>
              Processing Your Order
            </h3>
            <p className='mb-4 text-gray-600'>
              {processingSteps[processingStep]}
            </p>
            <div className='bg-gray-200 rounded-full w-full h-2.5'>
              <div
                className='bg-primary-500 rounded-full h-2.5 transition-all duration-500'
                style={{
                  width: `${
                    ((processingStep + 1) / processingSteps.length) * 100
                  }%`,
                }}
              ></div>
            </div>
            <p className='mt-4 text-gray-500 text-sm'>
              Please don't close this page
            </p>
          </div>
        </div>
      )}

      <div className='flex items-center mb-8'>
        <Link
          to='/cart'
          className='mr-3 text-text-primary hover:text-primary-500'
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className='font-poppins font-semibold text-2xl'>Checkout</h1>
      </div>

      {/* Error Display */}
      {error && (
        <div className='bg-red-50 mb-6 p-4 border border-red-200 rounded-lg'>
          <div className='flex items-start'>
            <AlertCircle className='mt-0.5 mr-2 w-5 h-5 text-red-500' />
            <div className='flex-1'>
              <p className='mb-3 text-red-700'>{error}</p>
              {error.includes("Unable to determine restaurant") && (
                <div className='flex sm:flex-row flex-col gap-2'>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      clearCart();
                      navigate("/restaurants");
                    }}
                  >
                    Clear Cart & Browse Restaurants
                  </Button>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      window.location.reload();
                    }}
                  >
                    Refresh Page
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Form Validation Errors */}
      {Object.keys(errors).length > 0 && (
        <div className='bg-yellow-50 mb-6 p-4 border border-yellow-200 rounded-lg'>
          <div className='flex items-start'>
            <AlertCircle className='mt-0.5 mr-2 w-5 h-5 text-yellow-500' />
            <div>
              <p className='mb-2 font-medium text-yellow-700'>
                Please fix the following errors:
              </p>
              <ul className='space-y-1 text-yellow-600 text-sm'>
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>• {error.message}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Debug Cart Info (Development Only) */}
      {process.env.NODE_ENV === "development" && (
        <div className='bg-blue-50 mb-6 p-4 border border-blue-200 rounded-lg'>
          <p className='mb-2 font-medium text-blue-700'>Debug - Cart State:</p>
          <div className='space-y-1 text-blue-600 text-sm'>
            <p>• Restaurant ID: {cart?.restaurantId || "None"}</p>
            <p>• Restaurant Name: {cart?.restaurantName || "None"}</p>
            <p>• Items Count: {cart?.items?.length || 0}</p>
            <p>• Cart Type: {typeof cart}</p>
            <p>• Items Array: {Array.isArray(cart?.items) ? "Yes" : "No"}</p>
          </div>
        </div>
      )}

      <div className='flex lg:flex-row flex-col gap-8'>
        {/* Checkout Form */}
        <div className='lg:w-2/3'>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Card className='mb-6'>
              <h2 className='flex items-center mb-4 font-poppins font-semibold text-lg'>
                <MapPin size={20} className='mr-2 text-primary-500' />
                Delivery Information
              </h2>

              <div className='space-y-4'>
                <FormControl
                  label='Full Name'
                  htmlFor='name'
                  error={errors.name?.message}
                  required
                >
                  <Input
                    id='name'
                    placeholder='Enter your full name'
                    error={errors.name?.message}
                    {...register("name", { required: "Full name is required" })}
                  />
                </FormControl>

                <FormControl
                  label='Phone Number'
                  htmlFor='phone'
                  error={errors.phone?.message}
                  required
                >
                  <Input
                    id='phone'
                    placeholder='Enter your phone number'
                    error={errors.phone?.message}
                    {...register("phone", {
                      required: "Phone number is required",
                      pattern: {
                        value: /^[0-9+\-\s()]*$/,
                        message: "Please enter a valid phone number",
                      },
                    })}
                  />
                </FormControl>

                {/* Address Selection */}
                <div className='space-y-4'>
                  <div className='flex justify-between items-center'>
                    <label className='block font-medium text-gray-700 text-sm'>
                      Delivery Address <span className='text-red-500'>*</span>
                    </label>
                    {selectedAddress && (
                      <div className='flex items-center text-green-600 text-xs'>
                        <MapPin size={12} className='mr-1' />
                        <span>Address Selected</span>
                      </div>
                    )}
                  </div>

                  <AddressSelector
                    onAddressSelect={(address) => {
                      selectAddress(address);
                      // Update form values for both address text and ID
                      setValue("address", address.address);
                      // Use backend ID for form submission
                      setValue("addressId", address.backendId || address.id);
                    }}
                  />

                  {/* Hidden inputs for form validation */}
                  <input
                    type='hidden'
                    {...register("address", {
                      required: "Please select a delivery address",
                      validate: (value) => {
                        if (!selectedAddress) {
                          return "Please select a delivery address.";
                        }

                        // Check if address is valid for delivery
                        const hasBackendId =
                          selectedAddress.backendId ||
                          (selectedAddress.id &&
                            !selectedAddress.id
                              .toString()
                              .startsWith("current_"));
                        const isValidForDelivery =
                          selectedAddress.type === "saved" ||
                          (selectedAddress.type === "current" &&
                            selectedAddress.coordinates) ||
                          hasBackendId;

                        if (!isValidForDelivery) {
                          return "Please select a saved delivery address or save your current location.";
                        }

                        // For current location, ensure it's saved
                        if (
                          selectedAddress.type === "current" &&
                          !selectedAddress.backendId
                        ) {
                          return "Please save your current location as a delivery address first.";
                        }

                        return true;
                      },
                    })}
                    value={selectedAddress?.address || ""}
                  />
                  <input
                    type='hidden'
                    {...register("addressId", {
                      required: "Address ID is required",
                      validate: (value) => {
                        if (!value) {
                          return "Please select a valid address";
                        }
                        return true;
                      },
                    })}
                    value={
                      selectedAddress?.backendId || selectedAddress?.id || ""
                    }
                  />

                  {(errors.address || errors.addressId) && (
                    <div className='bg-red-50 mt-2 p-3 border border-red-200 rounded-lg'>
                      <div className='flex items-center'>
                        <MapPin size={16} className='mr-2 text-red-500' />
                        <p className='font-medium text-red-700 text-sm'>
                          {errors.address?.message || errors.addressId?.message}
                        </p>
                      </div>
                      <p className='mt-1 ml-6 text-red-600 text-xs'>
                        Please select a delivery address from the list above or
                        add a new one.
                      </p>
                    </div>
                  )}
                </div>

                {/* Delivery Fee Information */}
                {hasSelectedAddress && (
                  <DeliveryFeeDisplay
                    deliveryInfo={deliveryInfo}
                    loading={deliveryLoading}
                    variant='detailed'
                    showBreakdown={true}
                  />
                )}

                <FormControl
                  label='Additional Notes (Optional)'
                  htmlFor='notes'
                  error={errors.notes?.message}
                >
                  <textarea
                    id='notes'
                    placeholder='Delivery instructions, apartment number, etc.'
                    className='bg-white px-4 py-2 border border-gray-300 focus:border-transparent rounded-md outline-none focus:ring-2 focus:ring-primary-500 w-full transition-colors duration-200'
                    rows={2}
                    {...register("notes")}
                  ></textarea>
                </FormControl>
              </div>
            </Card>

            <Card className='mb-6'>
              <h2 className='flex items-center mb-4 font-poppins font-semibold text-lg'>
                <CreditCard size={20} className='mr-2 text-primary-500' />
                Payment Method
              </h2>

              <div className='bg-gray-50 p-4 border border-gray-200 rounded-md'>
                <label className='flex items-center mb-2'>
                  <input
                    type='radio'
                    name='paymentMethod'
                    value='cash_on_delivery'
                    className='border-gray-300 focus:ring-primary-500 w-4 h-4 text-primary-500'
                    checked={selectedPaymentMethod === "cash_on_delivery"}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  />
                  <span className='ml-2 font-medium'>Cash on Delivery</span>
                </label>
                <p className='pl-6 text-text-secondary text-sm'>
                  Pay with cash when your order arrives
                </p>
              </div>

              <div className='bg-yellow-50 mt-4 p-4 border-yellow-400 border-l-4 rounded-md'>
                <div className='flex'>
                  <AlertCircle
                    size={20}
                    className='flex-shrink-0 mr-2 text-yellow-600'
                  />
                  <div className='text-yellow-800 text-sm'>
                    <p>
                      Please have the exact amount ready when your order
                      arrives.
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Order Timing */}
            <Card className='mb-6'>
              <h2 className='flex items-center mb-4 font-poppins font-semibold text-lg'>
                <Clock size={20} className='mr-2 text-primary-500' />
                Order Timing
              </h2>

              <div className='space-y-3'>
                <div
                  className={`border rounded-md p-4 cursor-pointer transition-colors ${
                    !isScheduled
                      ? "border-primary-500 bg-primary-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={handleOrderNow}
                >
                  <label className='flex items-center cursor-pointer'>
                    <input
                      type='radio'
                      name='orderTiming'
                      checked={!isScheduled}
                      onChange={handleOrderNow}
                      className='border-gray-300 focus:ring-primary-500 w-4 h-4 text-primary-500'
                    />
                    <Clock size={20} className='mr-2 ml-3 text-green-600' />
                    <div>
                      <span className='font-medium'>Order Now</span>
                      <p className='text-text-secondary text-sm'>
                        Deliver as soon as possible (30-45 mins)
                      </p>
                    </div>
                  </label>
                </div>

                <div
                  className={`border rounded-md p-4 cursor-pointer transition-colors ${
                    isScheduled
                      ? "border-primary-500 bg-primary-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => setShowScheduleModal(true)}
                >
                  <label className='flex items-center cursor-pointer'>
                    <input
                      type='radio'
                      name='orderTiming'
                      checked={isScheduled}
                      onChange={() => setShowScheduleModal(true)}
                      className='border-gray-300 focus:ring-primary-500 w-4 h-4 text-primary-500'
                    />
                    <Calendar size={20} className='mr-2 ml-3 text-blue-600' />
                    <div>
                      <span className='font-medium'>Schedule for Later</span>
                      <p className='text-text-secondary text-sm'>
                        Choose a specific date and time
                      </p>
                    </div>
                  </label>
                </div>

                {isScheduled && selectedDateTime && (
                  <div className='bg-blue-50 mt-3 p-3 border border-blue-200 rounded-lg'>
                    <div className='flex justify-between items-center'>
                      <div>
                        <p className='font-medium text-blue-800'>
                          Scheduled for:
                        </p>
                        <p className='text-blue-700 text-sm'>
                          {selectedDateTime.toLocaleDateString("en-US", {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}{" "}
                          at{" "}
                          {selectedDateTime.toLocaleTimeString("en-US", {
                            hour: "numeric",
                            minute: "2-digit",
                            hour12: true,
                          })}
                        </p>
                      </div>
                      <Button
                        variant='outline'
                        size='small'
                        onClick={() => setShowScheduleModal(true)}
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <div className='hidden lg:block'>
              <Button
                type='submit'
                variant='primary'
                size='large'
                fullWidth
                loading={loading}
                disabled={!hasSelectedAddress || loading}
              >
                Place Order
              </Button>
            </div>
          </form>
        </div>

        {/* Order Summary */}
        <div className='lg:w-1/3'>
          <Card>
            <h3 className='mb-4 font-poppins font-semibold text-lg'>
              Order Summary
            </h3>

            <div className='mb-4 text-sm'>
              <div className='mb-2 font-medium'>From {cart.restaurantName}</div>

              <div className='space-y-3'>
                {cart.items.map((item) => (
                  <div key={item.id} className='flex justify-between'>
                    <span>
                      {item.quantity}x {item.name}
                    </span>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className='space-y-3 mt-4 pt-4 border-gray-100 border-t text-sm'>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Subtotal</span>
                <span>${cart.subtotal.toFixed(2)}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Delivery Fee</span>
                <span>
                  {deliveryLoading ? (
                    <div className='bg-gray-200 rounded w-12 h-4 animate-pulse'></div>
                  ) : deliveryFee === 0 ? (
                    <span className='text-green-600'>Free</span>
                  ) : (
                    `$${deliveryFee.toFixed(2)}`
                  )}
                </span>
              </div>
              <div className='flex justify-between pt-3 border-gray-100 border-t font-semibold text-base'>
                <span>Total</span>
                <span>
                  {deliveryLoading ? (
                    <div className='bg-gray-200 rounded w-16 h-4 animate-pulse'></div>
                  ) : (
                    `$${(cart.subtotal + (deliveryFee || 0)).toFixed(2)}`
                  )}
                </span>
              </div>

              {/* Loyalty Points Preview */}
              {loyaltyData && (
                <div className='bg-orange-50 mt-4 p-3 border border-orange-200 rounded-lg'>
                  <div className='flex justify-between items-center text-sm'>
                    <span className='font-medium text-orange-800'>
                      Points you'll earn:
                    </span>
                    <span className='font-bold text-orange-600'>
                      +
                      {Math.floor(
                        cart.total *
                          LOYALTY_TIERS[loyaltyData.tier].pointsMultiplier
                      )}{" "}
                      pts
                    </span>
                  </div>
                  <div className='mt-1 text-orange-600 text-xs'>
                    {LOYALTY_TIERS[loyaltyData.tier].name} member:{" "}
                    {LOYALTY_TIERS[loyaltyData.tier].pointsMultiplier}x points
                  </div>
                </div>
              )}
            </div>

            <div className='lg:hidden mt-6'>
              <Button
                type='button'
                variant='primary'
                size='large'
                fullWidth
                loading={loading}
                onClick={handleSubmit(onSubmit)}
              >
                Place Order
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Schedule Order Modal */}
      <ScheduleOrderModal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        restaurantId={cart.restaurantId}
        orderData={{
          items: cart.items,
          total: cart.total,
          restaurantName: cart.restaurantName,
        }}
        onSchedule={handleScheduleOrder}
      />
    </div>
  );
};

export default Checkout;
